# -*- coding: utf-8 -*-

from odoo import fields, models


class HrEmployeePublic(models.Model):
    """
    توسيع نموذج hr.employee.public لإضافة الحقول المفقودة
    هذا يحل مشكلة الأخطاء المتعلقة بالحقول غير الموجودة
    """
    _inherit = "hr.employee.public"

    # الحقول المتعلقة بجواز السفر من مديول hr_employees_masarat
    passport_issue_location = fields.Char(
        string='Passport Issue Location',
        related='employee_id.passport_issue_location',
        readonly=True,
        help="مكان إصدار جواز السفر"
    )
    
    passport_issue_date = fields.Date(
        string="Passport Issue Date",
        related='employee_id.passport_issue_date',
        readonly=True,
        help="تاريخ إصدار جواز السفر"
    )
    
    passport_end_date = fields.Date(
        string="Passport End Date",
        related='employee_id.passport_end_date',
        readonly=True,
        help="تاريخ انتهاء جواز السفر"
    )
    
    referral_date = fields.Date(
        string="Referral Date",
        related='employee_id.referral_date',
        readonly=True,
        help="تاريخ الإحالة"
    )
    
    national_number = fields.Char(
        string="National Number",
        related='employee_id.national_number',
        readonly=True,
        help="الرقم الوطني"
    )
    
    residence_place = fields.Char(
        string="Residence Place",
        related='employee_id.residence_place',
        readonly=True,
        help="مكان الإقامة"
    )
    
    city = fields.Char(
        string="City",
        related='employee_id.city',
        readonly=True,
        help="المدينة"
    )
    
    neighborhood = fields.Char(
        string="Neighborhood",
        related='employee_id.neighborhood',
        readonly=True,
        help="الحي"
    )

    # حقل نوع العقد
    connected_with_comp = fields.Selection(
        string="Type of Contract",
        related='employee_id.connected_with_comp',
        readonly=True,
        help="نوع العقد مع الشركة"
    )
    
    class_id = fields.Selection(
        string="Class ID",
        related='employee_id.class_id',
        readonly=True,
        help="الدرجة الوظيفية"
    )
    
    selection_type = fields.Selection(
        string="selection type",
        related='employee_id.selection_type',
        readonly=True,
        help="نوع التعين"
    )

    # حقول إضافية من مديول hr_employees_masarat
    join_date = fields.Date(
        string="Join Date",
        related='employee_id.join_date',
        readonly=True,
        help="تاريخ الانضمام"
    )

    hiring_date = fields.Date(
        string="Hiring Date",
        related='employee_id.hiring_date',
        readonly=True,
        help="تاريخ التوظيف"
    )

    english_name = fields.Char(
        string="English Name",
        related='employee_id.english_name',
        readonly=True,
        help="الاسم بالإنجليزية"
    )

    int_id = fields.Char(
        string="Employee ID",
        related='employee_id.int_id',
        readonly=True,
        help="رقم الموظف"
    )

    bloodtype = fields.Selection(
        string="Blood Type",
        related='employee_id.bloodtype',
        readonly=True,
        help="فصيلة الدم"
    )

    street_name = fields.Char(
        string="Street Name",
        related='employee_id.street_name',
        readonly=True,
        help="اسم الشارع"
    )

    closest_point = fields.Char(
        string="Closest Point",
        related='employee_id.closest_point',
        readonly=True,
        help="أقرب نقطة دالة"
    )
    
    name_emergency1 = fields.Char(
        string="Name of closest relative 1",
        related='employee_id.name_emergency1',
        readonly=True,
        help="اسم اقرب الاقارب"
    )
    
    phone_emergency1 = fields.Char(
        string="Phone of closest relative 1",
        related='employee_id.phone_emergency1',
        readonly=True,
        help="هاتف اقرب الاقارب"
    )
    
    relation_emergency1 = fields.Char(
        string="Relation of closest relative 1",
        related='employee_id.relation_emergency1',
        readonly=True,
        help="علاقة اقرب الاقارب"
    )
    
    address_emergency1 = fields.Char(
        string="Address of closest relative 1",
        related='employee_id.address_emergency1',
        readonly=True,
        help="عنوان اقرب الاقارب"
    )
    
    name_emergency2 = fields.Char(
        string="Name of closest relative 2",
        related='employee_id.name_emergency2',
        readonly=True,
        help="اسم اقرب الاقارب"
    )
    
    phone_emergency2 = fields.Char(
        string="Phone of closest relative 2",
        related='employee_id.phone_emergency2',
        readonly=True,
        help="هاتف اقرب الاقارب"
    )
    
    relation_emergency2 = fields.Char(
        string="Relation of closest relative 2",
        related='employee_id.relation_emergency2',
        readonly=True,
        help="علاقة اقرب الاقارب"
    )
    
    address_emergency2 = fields.Char(
        string="Address of closest relative 2",
        related='employee_id.address_emergency2',
        readonly=True,
        help="عنوان اقرب الاقارب"
    )
    
    health_certificate = fields.Char(
        string="Health certificate",
        related='employee_id.health_certificate',
        readonly=True,
        help="شهادة صحية"
    )
    
    health_certificate_date = fields.Date(
        string="Health certificate date",
        related='employee_id.health_certificate_date',
        readonly=True,
        help="تاريخ البدء"
    )
    
    health_certificate_expiry = fields.Date(
        string="Health certificate expiry",
        related='employee_id.health_certificate_expiry',
        readonly=True,
        help="تاريخ الانتهاء"
    )
    
    criminal_certificate = fields.Char(
        string="Criminal certificate",
        related='employee_id.criminal_certificate',
        readonly=True,
        help="شهادة جنائية"
    )
    
    criminal_certificate_date = fields.Date(
        string="Criminal certificate date",
        related='employee_id.criminal_certificate_date',
        readonly=True,
        help="تاريخ البدء"
    )
    
    criminal_certificate_expiry = fields.Date(
        string="Criminal certificate expiry",
        related='employee_id.criminal_certificate_expiry',
        readonly=True,
        help="تاريخ الانتهاء"
    )
    
    study_field_edited = fields.Char(
        string="Study field",
        related='employee_id.study_field_edited',
        readonly=True,
        help="مجال الدراسة"
    )
    
    certificate_issue_date = fields.Date(
        string="Certificate issue date",
        related='employee_id.certificate_issue_date',
        readonly=True,
        help="تاريخ اصدار الشهادة"
    )
    
    certificate_issue_place = fields.Char(
        string="Certificate issue place",
        related='employee_id.certificate_issue_place',
        readonly=True,
        help="مكان اصدار الشهادة"
    )
    
    # حقل معدل ساعة العمل الإضافي
    overtime_hour_rate = fields.Float(
        string="Over Time Hour Rate",
        related='employee_id.overtime_hour_rate',
        readonly=True,
        help="معدل ساعة العمل الإضافي"
    )
    
    supporting_family = fields.Boolean(
        string="Supporting Family",
        related='employee_id.supporting_family',
        readonly=True,
        help="دعم الأسرة"
    )
    
