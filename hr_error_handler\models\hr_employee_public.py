# -*- coding: utf-8 -*-

from odoo import fields, models


class HrEmployeePublic(models.Model):
    """
    توسيع نموذج hr.employee.public لإضافة الحقول المفقودة
    هذا يحل مشكلة الأخطاء المتعلقة بالحقول غير الموجودة
    """
    _inherit = "hr.employee.public"

    # الحقول المتعلقة بجواز السفر من مديول hr_employees_masarat
    passport_issue_location = fields.Char(
        string='Passport Issue Location',
        related='employee_id.passport_issue_location',
        readonly=True,
        help="مكان إصدار جواز السفر"
    )
    
    passport_issue_date = fields.Date(
        string="Passport Issue Date",
        related='employee_id.passport_issue_date',
        readonly=True,
        help="تاريخ إصدار جواز السفر"
    )
    
    passport_end_date = fields.Date(
        string="Passport End Date",
        related='employee_id.passport_end_date',
        readonly=True,
        help="تاريخ انتهاء جواز السفر"
    )
    
    referral_date = fields.Date(
        string="Referral Date",
        related='employee_id.referral_date',
        readonly=True,
        help="تاريخ الإحالة"
    )
    
    national_number = fields.Char(
        string="National Number",
        related='employee_id.national_number',
        readonly=True,
        help="الرقم الوطني"
    )
    
    residence_place = fields.Char(
        string="Residence Place",
        related='employee_id.residence_place',
        readonly=True,
        help="مكان الإقامة"
    )
    
    city = fields.Char(
        string="City",
        related='employee_id.city',
        readonly=True,
        help="المدينة"
    )
    
    neighborhood = fields.Char(
        string="Neighborhood",
        related='employee_id.neighborhood',
        readonly=True,
        help="الحي"
    )

    # حقل نوع العقد
    connected_with_comp = fields.Selection(
        string="Type of Contract",
        related='employee_id.connected_with_comp',
        readonly=True,
        help="نوع العقد مع الشركة"
    )

    # حقول إضافية من مديول hr_employees_masarat
    join_date = fields.Date(
        string="Join Date",
        related='employee_id.join_date',
        readonly=True,
        help="تاريخ الانضمام"
    )

    hiring_date = fields.Date(
        string="Hiring Date",
        related='employee_id.hiring_date',
        readonly=True,
        help="تاريخ التوظيف"
    )

    english_name = fields.Char(
        string="English Name",
        related='employee_id.english_name',
        readonly=True,
        help="الاسم بالإنجليزية"
    )

    int_id = fields.Char(
        string="Employee ID",
        related='employee_id.int_id',
        readonly=True,
        help="رقم الموظف"
    )

    bloodtype = fields.Selection(
        string="Blood Type",
        related='employee_id.bloodtype',
        readonly=True,
        help="فصيلة الدم"
    )

    street_name = fields.Char(
        string="Street Name",
        related='employee_id.street_name',
        readonly=True,
        help="اسم الشارع"
    )

    closest_point = fields.Char(
        string="Closest Point",
        related='employee_id.closest_point',
        readonly=True,
        help="أقرب نقطة دالة"
    )
