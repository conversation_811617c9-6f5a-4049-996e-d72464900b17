# -*- coding: utf-8 -*-

from odoo import fields, models


class HrEmployeePublic(models.Model):
    """
    توسيع نموذج hr.employee.public لإضافة الحقول المفقودة
    هذا يحل مشكلة الأخطاء المتعلقة بالحقول غير الموجودة
    """
    _inherit = "hr.employee.public"

    # الحقول المتعلقة بجواز السفر من مديول hr_employees_masarat
    passport_issue_location = fields.Char(
        string='Passport Issue Location',
        related='employee_id.passport_issue_location',
        readonly=True,
        help="مكان إصدار جواز السفر"
    )
    
    passport_issue_date = fields.Date(
        string="Passport Issue Date",
        related='employee_id.passport_issue_date',
        readonly=True,
        help="تاريخ إصدار جواز السفر"
    )
    
    passport_end_date = fields.Date(
        string="Passport End Date",
        related='employee_id.passport_end_date',
        readonly=True,
        help="تاريخ انتهاء جواز السفر"
    )
    
    referral_date = fields.Date(
        string="Referral Date",
        related='employee_id.referral_date',
        readonly=True,
        help="تاريخ الإحالة"
    )
    
    national_number = fields.Char(
        string="National Number",
        related='employee_id.national_number',
        readonly=True,
        help="الرقم الوطني"
    )
    
    residence_place = fields.Char(
        string="Residence Place",
        related='employee_id.residence_place',
        readonly=True,
        help="مكان الإقامة"
    )
    
    city = fields.Char(
        string="City",
        related='employee_id.city',
        readonly=True,
        help="المدينة"
    )
    
    neighborhood = fields.Char(
        string="Neighborhood",
        related='employee_id.neighborhood',
        readonly=True,
        help="الحي"
    )
