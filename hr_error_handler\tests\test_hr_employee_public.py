# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase


class TestHrEmployeePublic(TransactionCase):
    """اختبارات للتأكد من أن الحقول المضافة تعمل بشكل صحيح"""

    def setUp(self):
        super(TestHrEmployeePublic, self).setUp()
        
        # إنشاء موظف للاختبار
        self.employee = self.env['hr.employee'].create({
            'name': 'Test Employee',
            'passport_issue_location': 'Test Location',
            'national_number': '*********',
            'city': 'Test City',
            'connected_with_comp': '3',  # تعيين
            'english_name': 'Test Employee EN',
            'int_id': 'EMP001',
        })

    def test_passport_issue_location_field_exists(self):
        """اختبار وجود حقل passport_issue_location في hr.employee.public"""
        public_employee = self.env['hr.employee.public'].browse(self.employee.id)
        
        # التأكد من أن الحقل موجود ويمكن الوصول إليه
        self.assertTrue(hasattr(public_employee, 'passport_issue_location'))
        
        # التأكد من أن القيمة صحيحة
        self.assertEqual(public_employee.passport_issue_location, 'Test Location')

    def test_national_number_field_exists(self):
        """اختبار وجود حقل national_number في hr.employee.public"""
        public_employee = self.env['hr.employee.public'].browse(self.employee.id)
        
        # التأكد من أن الحقل موجود ويمكن الوصول إليه
        self.assertTrue(hasattr(public_employee, 'national_number'))
        
        # التأكد من أن القيمة صحيحة
        self.assertEqual(public_employee.national_number, '*********')

    def test_city_field_exists(self):
        """اختبار وجود حقل city في hr.employee.public"""
        public_employee = self.env['hr.employee.public'].browse(self.employee.id)

        # التأكد من أن الحقل موجود ويمكن الوصول إليه
        self.assertTrue(hasattr(public_employee, 'city'))

        # التأكد من أن القيمة صحيحة
        self.assertEqual(public_employee.city, 'Test City')

    def test_connected_with_comp_field_exists(self):
        """اختبار وجود حقل connected_with_comp في hr.employee.public"""
        public_employee = self.env['hr.employee.public'].browse(self.employee.id)

        # التأكد من أن الحقل موجود ويمكن الوصول إليه
        self.assertTrue(hasattr(public_employee, 'connected_with_comp'))

        # التأكد من أن القيمة صحيحة
        self.assertEqual(public_employee.connected_with_comp, '3')

    def test_english_name_field_exists(self):
        """اختبار وجود حقل english_name في hr.employee.public"""
        public_employee = self.env['hr.employee.public'].browse(self.employee.id)

        # التأكد من أن الحقل موجود ويمكن الوصول إليه
        self.assertTrue(hasattr(public_employee, 'english_name'))

        # التأكد من أن القيمة صحيحة
        self.assertEqual(public_employee.english_name, 'Test Employee EN')

    def test_all_added_fields_exist(self):
        """اختبار وجود جميع الحقول المضافة"""
        public_employee = self.env['hr.employee.public'].browse(self.employee.id)
        
        # قائمة الحقول المضافة
        added_fields = [
            'passport_issue_location',
            'passport_issue_date',
            'passport_end_date',
            'referral_date',
            'national_number',
            'residence_place',
            'city',
            'neighborhood',
            'connected_with_comp',
            'join_date',
            'hiring_date',
            'english_name',
            'int_id',
            'bloodtype',
            'street_name',
            'closest_point',
        ]
        
        # التأكد من وجود جميع الحقول
        for field_name in added_fields:
            self.assertTrue(
                hasattr(public_employee, field_name),
                f"Field {field_name} is missing from hr.employee.public"
            )
